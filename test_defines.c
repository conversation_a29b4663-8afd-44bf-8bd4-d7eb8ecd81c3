#include "include/app_config.h"
#include <stdio.h>

int main() {
#ifdef CONFIG_PRODUCT_ZB01_QIXI
    printf("CONFIG_PRODUCT_ZB01_QIXI is defined\n");
    printf("VERSION_PREFIX: %s\n", VERSION_PREFIX);
    printf("VERSION_STR: %s\n", VERSION_STR);
    printf("VERSION_SUFFIX: %s\n", VERSION_SUFFIX);
#ifdef CONFIG_ENABLE_BULK_MODE
    printf("CONFIG_ENABLE_BULK_MODE is defined\n");
#endif
#ifdef CAR_CAM_CASE
    printf("CAR_CAM_CASE is defined\n");
#endif
#else
    printf("CONFIG_PRODUCT_ZB01_QIXI is NOT defined\n");
#endif
    return 0;
}
