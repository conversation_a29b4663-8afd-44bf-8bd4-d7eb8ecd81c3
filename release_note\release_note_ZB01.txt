ZB01 客户信息:

1.哈曼 小峰管家 app

2.悟空青稞 高石云享 小程序

3.开乐 s3 小峰管家 app

===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.2 BULK 开乐

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.开乐 ISOC 无法出图, BULK 可以出图, 并且客户要求尽量 BULK
2.FPS 从 sensor 端降到15, 这也是开乐要求
3.在 app_config.h 中添加了下面的配置, 否则通话的时候锁端说话手机没声音
// #elif defined CONFIG_PRODUCT_ZB01
// #define HUSB_MODE
// // #define CONFIG_NLPFIX_ENABLE
// #define UAC_ISO_INTERVAL 4
// #define MIC_SamplingFrequency 1

<<说明>>
无
===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.0 ISOC 哈曼
2. ZB01_V1.0.1 ISOC 悟空青稞

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.创建 ZB01 效果文件，并适配各场景

<<说明>>
无
===============================================================