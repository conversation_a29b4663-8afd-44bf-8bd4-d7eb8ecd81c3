ZB01 客户信息:

1.哈曼 小峰管家 app

2.悟空青稞 高石云享 小程序

3.开乐 s3 小峰管家 app

4.七喜3.5 微信小程序 (长安中间按钮出现二维码使用微信扫码配网)

===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_QIXI_V1.0.3 ISOC 七喜3.5

<<特殊适配>>(某客户的独立适配)
1. 新增 CONFIG_PRODUCT_ZB01_QIXI 产品配置, 使用ISOC传输模式替代BULK模式

2. USB包大小修正: 将UVC_FIFO_TXMAXP从1023字节改为1024字节
   - 原因: 1023字节违反USB 2.0规范，某些主机兼容性差
   - 作用: 1024字节符合USB 2.0标准，提高主机兼容性和传输稳定性

3. 启用YUV420格式支持: 添加CONFIG_USE_YUV420配置
   - 原因: 七喜3.5客户端对YUV420格式有特殊需求
   - 作用：提供更好的视频格式兼容性

<<正常修改如下>>
1. 在 Kconfig 中添加 CONFIG_PRODUCT_ZB01_QIXI 配置选项
2. 在 Makefile 中添加对 CONFIG_PRODUCT_ZB01_QIXI 的支持
3. 在 app_config.h 中添加 ZB01_QIXI 产品的版本和配置定义
4. 配置为 ISOC 传输模式以优化视频传输性能
5. 添加 YUV420 支持以适配七喜3.5客户需求

<<说明>>
此版本专为七喜3.5客户定制，使用微信小程序配网方式，通过长按中间按钮生成二维码进行配网。
采用 ISOC 传输模式以提供更好的视频传输性能和稳定性。

===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.2 BULK 开乐

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.开乐 ISOC 无法出图, BULK 可以出图, 并且客户要求尽量 BULK
2.FPS 从 sensor 端降到15, 这也是开乐要求
3.在 app_config.h 中添加了下面的配置, 否则通话的时候锁端说话手机没声音
// #elif defined CONFIG_PRODUCT_ZB01
// #define HUSB_MODE
// // #define CONFIG_NLPFIX_ENABLE
// #define UAC_ISO_INTERVAL 4
// #define MIC_SamplingFrequency 1

<<说明>>
无
===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.0 ISOC 哈曼
2. ZB01_V1.0.1 ISOC 悟空青稞

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.创建 ZB01 效果文件，并适配各场景

<<说明>>
无
===============================================================