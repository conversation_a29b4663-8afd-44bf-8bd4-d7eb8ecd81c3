ZB01 客户信息:

1.哈曼 小峰管家 app

2.悟空青稞 高石云享 小程序

3.开乐 s3 小峰管家 app

4.七喜3.5 微信小程序 (长安中间按钮出现二维码使用微信扫码配网)

===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_QIXI_V1.0.3 ISOC 七喜3.5

<<特殊适配>>(某客户的独立适配)
1. 新增 CONFIG_PRODUCT_ZB01_QIXI 产品配置
   - 在 Kconfig 中添加: config PRODUCT_ZB01_QIXI bool "PRODUCT_ZB01_QIXI"
   - 在 Makefile 第185行添加: $(if $(findstring y, $(CONFIG_PRODUCT_ZB01_QIXI)),-DCONFIG_PRODUCT_ZB01_QIXI,\
   - 在 Makefile 第202行添加传感器选择: $(CONFIG_PRODUCT_ZB01)$(CONFIG_PRODUCT_ZB01_QIXI)
   - 在 Makefile 第211行添加ISP资源选择: $(CONFIG_PRODUCT_ZB01)$(CONFIG_PRODUCT_ZB01_QIXI)

2. USB传输模式从BULK切换到ISOC
   - ZB01原版本使用BULK模式(CONFIG_ENABLE_BULK_MODE定义)
   - ZB01_QIXI不定义CONFIG_ENABLE_BULK_MODE，自动使用ISOC模式
   - 在 usb_std_class_def.h 第97-101行添加专门配置:
     #if defined(CONFIG_PRODUCT_ZB01_QIXI)
     #define UVC_FIFO_TXMAXP             1024
     #else
     #define UVC_FIFO_TXMAXP             1023
     #endif

3. app_config.h 中的版本和配置适配
   - 第96行版本前缀: #elif defined(CONFIG_PRODUCT_ZB01) || defined(CONFIG_PRODUCT_ZB01_QIXI)
     VERSION_PREFIX "ZB01", VERSION_STR "V1.0.2" (共用ZB01版本信息)
   - 第142行版本后缀: #elif defined (CONFIG_PRODUCT_ZB01_QIXI)
     VERSION_SUFFIX "ISOC_QIXI" (区别于ZB01的"BULK_KL")
   - 第196-201行产品特定配置:
     #elif defined CONFIG_PRODUCT_ZB01_QIXI
     #define HUSB_MODE
     #define CONFIG_USE_YUV420  (新增YUV420支持)
     // #define CONFIG_NLPFIX_ENABLE
     #define UAC_ISO_INTERVAL 4
     #define MIC_SamplingFrequency 1

4. 传输性能优化配置
   - ISOC模式下UVC_FIFO_TXMAXP=1024字节(vs BULK的512字节)
   - HUSB_MODE下UVC_PKT_SPILT=3，实际传输能力=1024*3=3072字节/包
   - 相比ZB01原版本的512字节/包，传输效率提升6倍

<<正常修改如下>>
1. 在 Kconfig 中添加 CONFIG_PRODUCT_ZB01_QIXI 配置选项
2. 在 Makefile 中添加对 CONFIG_PRODUCT_ZB01_QIXI 的支持
3. 在 app_config.h 中添加 ZB01_QIXI 产品的版本和配置定义
4. 配置为 ISOC 传输模式以优化视频传输性能
5. 添加 YUV420 支持以适配七喜3.5客户需求

<<说明>>
此版本专为七喜3.5客户定制，使用微信小程序配网方式，通过长按中间按钮生成二维码进行配网。
采用 ISOC 传输模式以提供更好的视频传输性能和稳定性。

===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.2 BULK 开乐

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.开乐 ISOC 无法出图, BULK 可以出图, 并且客户要求尽量 BULK
2.FPS 从 sensor 端降到15, 这也是开乐要求
3.在 app_config.h 中添加了下面的配置, 否则通话的时候锁端说话手机没声音
// #elif defined CONFIG_PRODUCT_ZB01
// #define HUSB_MODE
// // #define CONFIG_NLPFIX_ENABLE
// #define UAC_ISO_INTERVAL 4
// #define MIC_SamplingFrequency 1

<<说明>>
无
===============================================================
<<版本对应客户>>(如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. ZB01_V1.0.0 ISOC 哈曼
2. ZB01_V1.0.1 ISOC 悟空青稞

<<特殊适配>>(某客户的独立适配)
无

<<正常修改如下>>
1.创建 ZB01 效果文件，并适配各场景

<<说明>>
无
===============================================================