
# make 编译并下载
# make VERBOSE=1 显示编译详细过程
# make clean 清除编译临时文件
#
# 注意： Linux 下编译方式：
#     1. 从 http://pkgman.jieliapp.com/doc/all 处找到下载链接
#     2. 下载后，解压到 /opt/jieli 目录下，保证
#       /opt/jieli/common/bin/clang 存在（注意目录层次）
#     3. 确认 ulimit -n 的结果足够大（建议大于8096），否则链接可能会因为打开文件太多而失败
#       可以通过 ulimit -n 8096 来设置一个较大的值
#

# 工具路径设置
ifeq ($(OS), Windows_NT)
CURRENT_DIR := $(shell cd)
# Windows 下工具链位置
TOOL_DIR := C:/JL/pi32/bin
CC    := clang.exe
CXX   := clang.exe
LD    := q32s-lto-wrapper.exe
AR    := q32s-lto-ar.exe
MKDIR := mkdir_win -p
RM    := rm -rf

SYS_LIB_DIR := C:/JL/pi32/q32s-lib
SYS_INC_DIR := C:/JL/pi32/q32s-include
EXT_CFLAGS  := # Windows 下不需要 -D__SHELL__
export PATH:=$(TOOL_DIR);$(PATH)

## 后处理脚本
POST_SCRIPT     := cpu\dv20\tools\post_build.bat
RUN_POST_SCRIPT := $(POST_SCRIPT)
else
CURRENT_DIR := $(shell pwd)

# Linux 下工具链位置
TOOL_DIR := /opt/jieli/q32s/bin
CC    := clang
CXX   := clang++
LD    := lto-wrapper
AR    := lto-ar
MKDIR := mkdir -p
RM    := rm -rf
export OBJDUMP := $(TOOL_DIR)/objdump
export OBJCOPY := $(TOOL_DIR)/objcopy
export OBJSIZEDUMP := $(TOOL_DIR)/objsizedump

SYS_LIB_DIR := $(TOOL_DIR)/../lib
SYS_INC_DIR := $(TOOL_DIR)/../include
EXT_CFLAGS  := -D__SHELL__ # Linux 下需要这个保证正确处理 download.c
export PATH:=$(TOOL_DIR):$(PATH)

## 后处理脚本
POST_SCRIPT     := cpu/dv20/tools/post_build.sh
RUN_POST_SCRIPT := bash $(POST_SCRIPT)
endif

include .config

CC  := $(TOOL_DIR)/$(CC)
CXX := $(TOOL_DIR)/$(CXX)
LD  := $(TOOL_DIR)/$(LD)
AR  := $(TOOL_DIR)/$(AR)
# 输出文件设置
OUT_ELF   := cpu/dv20/tools/sdk.elf
OBJ_FILE  := $(OUT_ELF).objs.txt
# 编译路径设置
BUILD_DIR := obj/Release
TOOLS_DIR := cpu/dv20/tools

# 编译参数设置
CFLAGS := \
	-flto \
	-target q32s \
	-integrated-as \
	-fno-builtin \
	-mllvm -inline-threshold=5 \
	-Oz \
	-integrated-as \
	-fallow-pointer-null \
	-fno-common \
	-flto \
	-g \
	-Oz \
	-mllvm -q32s-large-program \
	-Wno-format \
	-W \
	-Wno-unused-value \
	-Wno-unused-function \
	-Wno-unused-comparison \
	-Wno-unused-parameter \
	-Wno-missing-field-initializers \
	-Werror=implicit-int \
	-Werror=incompatible-pointer-types \
	-Werror=implicit-function-declaration


# C++额外的编译参数
CXXFLAGS :=


# 宏定义
DEFINES := \
	-DNOFLOAT \
	-DCONFIG_USE_USER_UVC_DRIVER_EN \
	-DCONFIG_USE_JPEG_DRIVER_EN \
	-DISP0_EN \
	-D__PRINTF_DEBUG \
	-D__CPU_dv20 \
	-D__ARCH_q32s \
	-DUSB_HW_20 \

DEFINES += $(EXT_CFLAGS) # 额外的一些定义

# 头文件搜索路径
INCLUDES := \
	-Iinclude \
	-Iinclude/generic \
	-Iinclude/include_lib \
	-Iinclude/fs \
	-Iinclude/drivers \
	-Iinclude/drivers/flash \
	-Iinclude/drivers/key \
	-Iinclude/drivers/eeprom/E2BApi \
	-Iinclude/include_lib/ejpeg_header_new \
	-Icpu/dv20 \
	-Iinclude/cpu/dv20 \
	-Iinclude/cpu/dv20/asm \
	-Iinclude/cpu/dv20/husb \
	-Iinclude/drivers/usb \
	-Iinclude/drivers/usb/device \
	-I$(SYS_INC_DIR) \

c_SRC_SENSOR_H63P := \
	drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi.c \
	drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_ae.c \
	drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_awb.c \
	drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_iq.c

c_SRC_SENSOR_SC1346 := \
	drivers/camera/sc1346_mipi/SC1346_mipi.c \
	drivers/camera/sc1346_mipi/SC1346_mipi_ae.c \
	drivers/camera/sc1346_mipi/SC1346_mipi_awb.c \
	drivers/camera/sc1346_mipi/SC1346_mipi_iq.c

c_SRC_SENSOR_BF20A1 := \
	drivers/camera/bf20a1_mipi_25FPS_VGA_20240715/bf20a1.c \
	drivers/camera/bf20a1_mipi_25FPS_VGA_20240715/bf20a1_ae.c \
	drivers/camera/bf20a1_mipi_25FPS_VGA_20240715/bf20a1_awb.c \
	drivers/camera/bf20a1_mipi_25FPS_VGA_20240715/bf20a1_iq.c

c_SRC_SENSOR_C2595_YU7L := \
	drivers/camera/c2595_mipi/c2595.c \
	drivers/camera/c2595_mipi/c2595_ae.c \
	drivers/camera/c2595_mipi/c2595_awb.c \
	drivers/camera/c2595_mipi/c2595_iq.c

c_SRC_SENSOR_OV02B_ZB01 := \
	drivers/camera/ov02b_mipi/ov02b.c \
	drivers/camera/ov02b_mipi/ov02b_ae.c \
	drivers/camera/ov02b_mipi/ov02b_awb.c \
	drivers/camera/ov02b_mipi/ov02b_iq.c

PRODUCT_DEFINES := $(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_DEFAULT)),-DCONFIG_PRODUCT_SU7PRO_DEFAULT,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_2ND)),-DCONFIG_PRODUCT_SU7PRO_2ND,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_USBV11)),-DCONFIG_PRODUCT_SU7PRO_USBV11,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)),-DCONFIG_PRODUCT_SU7PRO_AUDIO_16K,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)),-DCONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)),-DCONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_BULK)),-DCONFIG_PRODUCT_SU7PRO_BULK,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7Z_BULK)),-DCONFIG_PRODUCT_SU7Z_BULK,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_OUTPUT_864x480)),-DCONFIG_PRODUCT_S30_OUTPUT_864x480,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_7256)),-DCONFIG_PRODUCT_S30_7256,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_USBV11_DEFAULT)),-DCONFIG_PRODUCT_S30_USBV11_DEFAULT,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0)),-DCONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)),-DCONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)),-DCONFIG_PRODUCT_S30_COMPATIBLE_XISHANG,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK)),-DCONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7_VM0)),-DCONFIG_PRODUCT_YU7_VM0,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7_BULK)),-DCONFIG_PRODUCT_YU7_BULK,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7L_DEFAULT)),-DCONFIG_PRODUCT_YU7L_DEFAULT,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7L_XISHANG)),-DCONFIG_PRODUCT_YU7L_XISHANG,\
					$(if $(findstring y, $(CONFIG_PRODUCT_ZB01)),-DCONFIG_PRODUCT_ZB01,\
					-DCONFIG_PRODUCT_YU7_DEFAULT))))))))))))))))))))

DEFINES += $(PRODUCT_DEFINES)

is_product_s30 := $(if $(findstring y, $(CONFIG_PRODUCT_S30_OUTPUT_864x480)\
									   $(CONFIG_PRODUCT_S30_7256)\
									   $(CONFIG_PRODUCT_S30_USBV11_DEFAULT)\
									   $(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0)\
									   $(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)\
									   $(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)\
									   $(CONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK)),y,n)

get_sensor_src = $(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_DEFAULT)$(CONFIG_PRODUCT_SU7PRO_2ND)$(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)$(CONFIG_PRODUCT_SU7PRO_USBV11)$(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)$(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)$(CONFIG_PRODUCT_SU7PRO_BULK)$(CONFIG_PRODUCT_SU7Z_BULK)),\
						$(c_SRC_SENSOR_H63P),\
					$(if $(findstring y, $(is_product_s30)),$(c_SRC_SENSOR_BF20A1),\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7L_DEFAULT)$(CONFIG_PRODUCT_YU7L_XISHANG)),$(c_SRC_SENSOR_C2595_YU7L),\
					$(if $(findstring y, $(CONFIG_PRODUCT_ZB01)),$(c_SRC_SENSOR_OV02B_ZB01),\
						$(c_SRC_SENSOR_SC1346)))))

get_isp_res_dir = $(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_DEFAULT)$(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)$(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)$(CONFIG_PRODUCT_SU7PRO_BULK)$(CONFIG_PRODUCT_SU7Z_BULK)),isp_res_su7_default,\
					$(if $(findstring y, $(CONFIG_PRODUCT_SU7PRO_2ND)$(CONFIG_PRODUCT_SU7PRO_USBV11)$(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)),isp_res_su7_2nd,\
					$(if $(findstring y, $(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)$(CONFIG_PRODUCT_S30_USBV11_DEFAULT)),isp_res_s30_jianfeng,\
					$(if $(findstring y, $(is_product_s30)),isp_res_s30_default,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7_VM0)),isp_res_yu7_vm0,\
					$(if $(findstring y, $(CONFIG_PRODUCT_YU7L_DEFAULT)$(CONFIG_PRODUCT_YU7L_XISHANG)),isp_res_YU7L,\
					$(if $(findstring y, $(CONFIG_PRODUCT_ZB01)),isp_res_ZB01,\
					isp_res_yu7_default)))))))

# get_key = $(if $(findstring y, $(is_product_s30)),V91AC5200-4DE6.key,Hopewell_AC5200-9330.key)
get_key := Hopewell_AC5200-9330.key

ifeq (,$(filter clean menuconfig get_fw_version,$(MAKECMDGOALS)))
$(info PRODUCT_DEFINES:$(PRODUCT_DEFINES))
$(info get_sensor_src: $(if $(findstring h63p, $(get_sensor_src)),H63P, \
	$(if $(findstring c2595,$(get_sensor_src)),C2595, \
	$(if $(findstring bf20a1,$(get_sensor_src)),BF20A1, \
	$(if $(findstring ov02b,$(get_sensor_src)),OV02B, \
	SC1346)))))
$(info get_isp_res_dir:$(get_isp_res_dir))
endif
# 获取所有Makefile文件
MAKEFILES := $(MAKEFILE_LIST)

# 将Makefile添加到依赖
define add_makefile_deps
$(foreach t,$(1),$(eval $(t): $(MAKEFILES)))
endef

# 需要编译的 .c 文件
c_SRC_FILES := \
	app/car_cam_case/video.c \
	app/car_cam_case/video_bulk.c \
	app/device_change.c \
	app/isp_scenes.c \
	app/main.c \
	app/osd.c \
	app/pc_cam_case/video.c \
	app/pc_cam_case/video2.c \
	app/pc_cam_case/video_bulk.c \
	app/test/audio_test.c \
	app/test/nlpfix_test.c \
	app/test/sleep_test.c \
	cpu/dv20/board/board_AC5316A_DEV_20211231.c \
	cpu/dv20/exception.c \
	cpu/dv20/gpio.c \
	cpu/dv20/image_capture.c \
	cpu/dv20/ldo.c \
	cpu/dv20/setup.c \
	cpu/dv20/timer_pwm.c \
	cpu/dv20/uart.c \
	cpu/dv20/user.c \
	$(get_sensor_src) \
	drivers/common/printf-stdarg.c \
	drivers/common/rand.c \
	drivers/delay.c \
	drivers/device_api.c \
	drivers/key/adkey.c \
	drivers/key/iokey.c \
	drivers/key/key_driver.c \
	drivers/usb/device/msd.c \
	drivers/usb/device/task_pc.c \
	drivers/usb/device/uac1.c \
	drivers/usb/device/uac_stream.c \
	drivers/usb/device/uvc.c \
	drivers/usb/device/uvc_user_extension.c \
	drivers/usb/device/uvc_video_interface.c \
	drivers/usb/usb_config.c \
	drivers/usb_audio.c \

# 需要编译的 .S 文件
S_SRC_FILES := \
	cpu/dv20/startup.S \


# 需要编译的 .s 文件
s_SRC_FILES :=


# 需要编译的 .cpp 文件
cpp_SRC_FILES :=


# 需要编译的 .cc 文件
cc_SRC_FILES :=


# 需要编译的 .cxx 文件
cxx_SRC_FILES :=

# 链接脚本
ld_script := cpu/dv20/ram.ld

# 链接参数
LFLAGS := \
	--plugin-opt=-dont-used-symbol-list=malloc,free,sprintf,printf,puts,putchar,perror,vprintf,printi,fopen,fread \
	-Map=map.txt \
	-T$(ld_script) \
	--plugin-opt=-inline-threshold=5 \
	--plugin-opt=-enable-ipra=true \
	--plugin-opt=-global-merge-on-const \
	--plugin-opt=-inline-normal-into-special-section=true \
	--plugin-opt=-q32s-large-program \
	--start-group \
	liba/cpu.a \
	liba/lib_usb_syn.a \
	liba/jpeg_encoder.a \
	liba/cbuf.a \
	liba/lbuf.a \
	--whole-archive \
	--no-whole-archive \
	liba/mm.a \
	liba/database.a \
	liba/isp.a \
	liba/nlpfix/libFFT_pi32v2_OnChip.a \
	liba/nlpfix/libEchoSuppress_fix_pi32v2_OnChip.a \
	liba/nlpfix/libSplittingFilter_pi32v2_small_OnChip.a \
	--end-group \
	-flto \
	--plugin-opt=-inline-threshold=5 \


LIBPATHS := \
	-L$(SYS_LIB_DIR) \


LIBS := \
	$(SYS_LIB_DIR)/libm.a \
	$(SYS_LIB_DIR)/libc.a \
	$(SYS_LIB_DIR)/libm.a \
	$(SYS_LIB_DIR)/libcompiler-rt.a \



c_OBJS    := $(c_SRC_FILES:%.c=%.o)
S_OBJS    := $(S_SRC_FILES:%.S=%.o)
cpp_OBJS  := $(cpp_SRC_FILES:%.cpp=%.o)
cxx_OBJS  := $(cxx_SRC_FILES:%.cxx=%.o)
cc_OBJS   := $(cc_SRC_FILES:%.cc=%.o)

OBJS      := $(c_OBJS) $(S_OBJS) $(s_OBJS) $(cpp_OBJS) $(cxx_OBJS) $(cc_OBJS)
DEP_FILES := $(OBJS:%.o=%.d)


OBJS      := $(addprefix $(BUILD_DIR)/, $(OBJS))
DEP_FILES := $(addprefix $(BUILD_DIR)/, $(DEP_FILES))


VERBOSE ?= 0
ifeq ($(VERBOSE), 1)
QUITE :=
else
QUITE := @
endif

# 一些旧的 make 不支持 file 函数，需要 make 的时候指定 LINK_AT=0 make
LINK_AT ?= 1

# 表示下面的不是一个文件的名字，无论是否存在 all, clean 这样的文件
# 还是要执行命令
# see: https://www.gnu.org/software/make/manual/html_node/Phony-Targets.html
.PHONY: all clean

# 不要使用 make 预设置的规则
# see: https://www.gnu.org/software/make/manual/html_node/Suffix-Rules.html
.SUFFIXES:

all: flash

# 所有OBJ依赖于Makefile
$(call add_makefile_deps,$(OBJS))

flash: post_build
	$(QUITE) echo use isp res in $(get_isp_res_dir)
	cd cpu/dv20/tools && isd_download.exe -tonorflash -dev dv20 -boot 0x4000 -div8 -wait 300 -uboot uboot.boot -app app.bin cfg_tool.bin -tone $(get_isp_res_dir)/dir_isp_res -uvc DV20,USB 2 -reboot 100 -key $(get_key) || true

post_build: $(OUT_ELF)
	$(info +POST-BUILD)
	$(QUITE) $(RUN_POST_SCRIPT) sdk

clean:
	$(QUITE) $(RM) $(OUT_ELF)
	$(QUITE) $(RM) $(BUILD_DIR)
	$(QUITE) $(RM) $(TOOLS_DIR)/jl_isd.fw $(TOOLS_DIR)/jl_isd.bin

generate_isp_res:
	cd cpu/dv20/tools/$(strip $(get_isp_res_dir)) && ..\generate_isp_res.bat

menuconfig:
	pip install -i https://pypi.tuna.tsinghua.edu.cn/simple windows-curses kconfiglib
	set MENUCONFIG_STYLE=aquatic && python -m menuconfig

get_fw_version: include/app_config.h
	@powershell -Command "$$defs = & '$(CC)' $(CFLAGS) $(DEFINES) $(INCLUDES) -dM -E -P $<; \
	foreach ($$line in $$defs) { \
		if ($$line -match '#define VERSION_PREFIX\s+\"(.+?)\"') { $$prefix = $$matches[1] } \
		if ($$line -match '#define VERSION_STR\s+\"(.+?)\"')    { $$str    = $$matches[1] } \
		if ($$line -match '#define VERSION_SUFFIX\s+\"(.+?)\"') { $$suffix = $$matches[1] } \
	}; \
	Write-Output Version:$${prefix}_$${str}_$${suffix}

ifeq ($(LINK_AT), 1)
$(OUT_ELF): $(OBJS) $(ld_script)
	$(info +LINK $@)
	$(shell $(MKDIR) $(@D))
	$(file >$(OBJ_FILE), $(OBJS))
	$(QUITE) $(LD) -o $(OUT_ELF) @$(OBJ_FILE) $(LFLAGS) $(LIBPATHS) $(LIBS)
	
else
$(OUT_ELF): $(OBJS) $(ld_script)
	$(info +LINK $@)
	$(shell $(MKDIR) $(@D))
	$(QUITE) $(LD) -o $(OUT_ELF) $(OBJS) $(LFLAGS) $(LIBPATHS) $(LIBS)
endif


$(BUILD_DIR)/%.o : %.c
	@$(MKDIR) $(@D)
	@$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@
	

$(BUILD_DIR)/%.o : %.S
	@$(MKDIR) $(@D)
	@$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@
	
$(BUILD_DIR)/%.o : %.s
	$(info +AS $<)
	@$(MKDIR) $(@D)
	@$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

$(BUILD_DIR)/%.o : %.cpp
	$(info +CXX $<)
	@$(MKDIR) $(@D)
	@$(CXX) $(CFLAGS) $(CXXFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CXX) $(CXXFLAGS) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

$(BUILD_DIR)/%.o : %.cxx
	$(info +CXX $<)
	@$(MKDIR) $(@D)
	@$(CXX) $(CFLAGS) $(CXXFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CXX) $(CXXFLAGS) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

$(BUILD_DIR)/%.o : %.cc
	$(info +CXX $<)
	@$(MKDIR) $(@D)
	@$(CXX) $(CFLAGS) $(CXXFLAGS) $(DEFINES) $(INCLUDES) -MM -MT $@ $< -o $(@:.o=.d)
	$(info Building $<)
	$(QUITE) $(CXX) $(CXXFLAGS) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

$(ld_script): cpu/dv20/ram_ld.c $(MAKEFILES)
	$(info Building $@)
	$(QUITE) $(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -D__LD__ -E -P $< -o $@

-include $(DEP_FILES)
